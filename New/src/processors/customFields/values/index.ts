/**
 * Field Value Conversion System
 * 
 * This module provides a complete field value conversion system for bidirectional
 * synchronization between AutoPatient (AP) and CliniCore (CC) systems.
 * 
 * Key Features:
 * - TEXTBOX_LIST conversion for CC multi-value fields
 * - Boolean ↔ RADIO conversion with international support
 * - Medical field handling (medication, diagnoses, recommendations)
 * - Select field mapping (single/multi)
 * - Standard field conversion (email, phone)
 * - Comprehensive validation and error handling
 * 
 * Usage:
 * ```typescript
 * import { FieldValueConversionService, convertCCToAP, convertAPToCC } from './values';
 * 
 * const service = new FieldValueConversionService();
 * const result = await service.convertCCToAP(ccField, ccPatientField, apField);
 * ```
 */

// Export all types
export type {
  APFieldType,
  CCFieldType,
  CCFieldValue,
  APTextboxListValue,
  APCustomFieldValue,
  ConversionDirection,
  ConversionContext,
  ConversionResult,
  ConversionError,
  ValidationRule,
  ValidationResult,
  OptionMapping,
  BooleanConversionOptions,
  TextboxListConfig,
  MultiValueStrategy,
  FieldConversionConfig,
  BatchConversionRequest,
  BatchConversionResult,
  StandardFieldMapping,
  ConversionMetrics,
  CacheConfig,
  ConversionServiceConfig
} from "./types";

// Export ConversionErrorType as both type and value
export { ConversionErrorType } from "./types";

// Export validation utilities
export {
  VALIDATION_RULES,
  FIELD_COMPATIBILITY_MATRIX,
  normalizePhone,
  normalizeEmail,
  normalizeDate,
  validateFieldValue,
  validateMultipleValues,
  isEmpty,
  createValidationError,
  areFieldTypesCompatible
} from "./validation";

// Export core conversion functions
export {
  DEFAULT_BOOLEAN_OPTIONS,
  convertCCMultiValueToAPTextboxList,
  convertAPTextboxListToCCMultiValue,
  convertCCBooleanToAPRadio,
  convertAPRadioToCCBoolean,
  createBooleanRadioOptions,
  convertSingleValue
} from "./converters";

// Export main conversion service
export {
  FieldValueConversionService,
  DEFAULT_CONVERSION_CONFIG
} from "./conversionService";

// Import for internal use
import { FieldValueConversionService } from "./conversionService";
import { areFieldTypesCompatible } from "./validation";
import { DEFAULT_BOOLEAN_OPTIONS, createBooleanRadioOptions } from "./converters";

// Export utility functions
export {
  STANDARD_FIELD_MAPPINGS,
  findStandardFieldMapping,
  createOptionMappings,
  generateTextboxListOptions,
  convertStandardFieldValue,
  generateOptionIds,
  supportsMultiValue,
  getFieldTypePriority,
  createReferenceFieldValue,
  mergeConversionResults,
  cleanFieldName,
  areValuesEquivalent
} from "./utils";

/**
 * Convenience function to create a new conversion service with default config
 */
export function createConversionService(config?: Partial<import("./types").FieldConversionConfig>) {
  return new FieldValueConversionService(config);
}

/**
 * Quick conversion function for CC to AP (convenience wrapper)
 */
export async function convertCCToAP(
  ccField: import("@/type").GetCCCustomField,
  ccPatientField: import("@/type").GetCCPatientCustomField,
  apField: import("@/type").APGetCustomFieldType,
  config?: Partial<import("./types").FieldConversionConfig>
) {
  const service = new FieldValueConversionService(config);
  return service.convertCCToAP(ccField, ccPatientField, apField);
}

/**
 * Quick conversion function for AP to CC (convenience wrapper)
 */
export async function convertAPToCC(
  apField: import("@/type").APGetCustomFieldType,
  apValue: import("./types").APCustomFieldValue,
  ccField: import("@/type").GetCCCustomField,
  config?: Partial<import("./types").FieldConversionConfig>
) {
  const service = new FieldValueConversionService(config);
  return service.convertAPToCC(apField, apValue, ccField);
}

/**
 * Validate field type compatibility
 */
export function validateFieldCompatibility(
  sourceType: import("./types").APFieldType | import("./types").CCFieldType,
  targetType: import("./types").APFieldType | import("./types").CCFieldType
): boolean {
  return areFieldTypesCompatible(sourceType, targetType);
}

/**
 * Get recommended AP field type for CC field
 */
export function getRecommendedAPFieldType(
  ccField: import("@/type").GetCCCustomField
): import("./types").APFieldType {
  const ccType = ccField.type as import("./types").CCFieldType;
  
  // Multi-value handling - all CC multi-value fields → AP TEXTBOX_LIST
  if (ccField.allowMultipleValues) {
    // Exception: CC select (multi) → AP MULTIPLE_OPTIONS for semantic accuracy
    if (ccType === "select" || ccType === "select-or-custom") {
      return "MULTIPLE_OPTIONS";
    }
    return "TEXTBOX_LIST";
  }

  // Single value mappings
  switch (ccType) {
    case "text":
      return "TEXT";
    case "textarea":
      return "LARGE_TEXT";
    case "number":
      return "NUMERICAL";
    case "telephone":
      return "PHONE";
    case "email":
      return "EMAIL";
    case "date":
      return "DATE";
    case "select":
    case "select-or-custom":
      return "SINGLE_OPTIONS";
    case "boolean":
      return "RADIO";
    case "medication":
    case "permanent-diagnoses":
    case "patient-has-recommended":
      return "TEXT";
    default:
      return "TEXT"; // Fallback
  }
}

/**
 * Get recommended CC field type for AP field
 */
export function getRecommendedCCFieldType(
  apField: import("@/type").APGetCustomFieldType
): { type: import("./types").CCFieldType; allowMultipleValues: boolean } {
  const apType = apField.dataType as import("./types").APFieldType;
  
  switch (apType) {
    case "TEXT":
      return { type: "text", allowMultipleValues: false };
    case "LARGE_TEXT":
      return { type: "textarea", allowMultipleValues: false };
    case "NUMERICAL":
      return { type: "number", allowMultipleValues: false };
    case "PHONE":
      return { type: "telephone", allowMultipleValues: false };
    case "EMAIL":
      return { type: "email", allowMultipleValues: false };
    case "DATE":
      return { type: "date", allowMultipleValues: false };
    case "MONETORY":
      return { type: "text", allowMultipleValues: false };
    case "SINGLE_OPTIONS":
      return { type: "select", allowMultipleValues: false };
    case "MULTIPLE_OPTIONS":
      return { type: "select", allowMultipleValues: true };
    case "CHECKBOX":
      return { type: "select", allowMultipleValues: true };
    case "RADIO":
      return { type: "select", allowMultipleValues: false };
    case "TEXTBOX_LIST":
      return { type: "text", allowMultipleValues: true };
    default:
      return { type: "text", allowMultipleValues: false }; // Fallback
  }
}

/**
 * Create boolean RADIO field configuration for AP
 */
export function createBooleanRadioFieldConfig(
  name: string,
  options?: Partial<import("./types").BooleanConversionOptions>
): Partial<import("@/type").APPostCustomfieldType> {
  const booleanOptions = { ...DEFAULT_BOOLEAN_OPTIONS, ...options };

  return {
    name,
    dataType: "RADIO",
    options: createBooleanRadioOptions(booleanOptions)
  };
}

/**
 * Create TEXTBOX_LIST field configuration for AP
 */
export function createTextboxListFieldConfig(
  name: string,
  values: string[],
  config?: Partial<import("./types").TextboxListConfig>
): Partial<import("@/type").APPostCustomfieldType> {
  const textboxConfig = {
    generateOptionIds: true,
    preserveOrder: true,
    allowEmptyValues: false,
    maxOptions: 100,
    ...config
  };

  const options = values
    .filter(v => v && v.trim() !== "")
    .slice(0, textboxConfig.maxOptions)
    .map((value, index) => ({
      label: `Value ${index + 1}`,
      prefillValue: value
    }));

  return {
    name,
    dataType: "TEXTBOX_LIST",
    textBoxListOptions: options
  };
}

/**
 * Default export: the main conversion service class
 */
export { FieldValueConversionService as default } from "./conversionService";
