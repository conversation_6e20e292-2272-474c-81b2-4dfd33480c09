# Field Value Conversion System

This directory contains the complete field value conversion system for bidirectional synchronization between AutoPatient (AP) and CliniCore (CC) systems, implementing the specifications defined in `field-conversion-specification.md`.

## Overview

The value conversion system handles the transformation of field values during synchronization, ensuring data integrity and semantic meaning preservation across platforms. It supports all field types and conversion scenarios defined in the specification.

## Key Features

### 🔄 **TEXTBOX_LIST Conversion**
- All CC multi-value fields convert to AP TEXTBOX_LIST using `Record<string, string>` structure
- Option IDs as keys for proper AP field value mapping
- No pipe separator fallbacks (per specification)

### ✅ **Boolean Conversion**
- CC boolean ↔ AP RADIO conversion with international support
- `true` → "Yes", `false` → "No" with options ["Yes", "Ja", "No", "Nein"]
- Bidirectional conversion with fuzzy matching

### 🏥 **Medical Field Handling**
- Single value medical fields (medication, permanent-diagnoses, patient-has-recommended) → TEXT
- Multi-value medical fields → TEXTBOX_LIST
- Preserves medical data integrity

### 📋 **Select Field Mapping**
- CC select (single) → AP SINGLE_OPTIONS
- CC select (multi) → AP MULTIPLE_OPTIONS
- Option value mapping and validation

### 📧 **Standard Field Mapping**
- AP standard fields (email, phone) ↔ CC custom fields conversion
- Proper normalization and validation
- Cross-platform reference field creation

## Architecture

```
values/
├── types.ts              # Core type definitions
├── validation.ts         # Validation utilities and rules
├── converters.ts         # Core conversion algorithms
├── conversionService.ts  # Main orchestration service
├── utils.ts             # Utility functions and helpers
├── examples.ts          # Usage examples and demonstrations
├── index.ts             # Public API exports
└── README.md            # This documentation
```

## Usage

### Basic Usage

```typescript
import { FieldValueConversionService, convertCCToAP } from './values';

// Create service instance
const service = new FieldValueConversionService();

// Convert CC field to AP
const result = await service.convertCCToAP(ccField, ccPatientField, apField);

if (result.success) {
  console.log('Converted value:', result.value);
} else {
  console.error('Conversion failed:', result.error);
}
```

### Convenience Functions

```typescript
import { convertCCToAP, convertAPToCC } from './values';

// Quick CC to AP conversion
const ccToApResult = await convertCCToAP(ccField, ccPatientField, apField);

// Quick AP to CC conversion
const apToCcResult = await convertAPToCC(apField, apValue, ccField);
```

### Field Type Recommendations

```typescript
import { getRecommendedAPFieldType, getRecommendedCCFieldType } from './values';

// Get recommended AP field type for CC field
const apType = getRecommendedAPFieldType(ccField);

// Get recommended CC field type for AP field
const ccType = getRecommendedCCFieldType(apField);
```

## Conversion Examples

### Example 1: CC Multi-Value → AP TEXTBOX_LIST

```typescript
// CC multi-value text field (allergies)
const ccField = {
  id: 123,
  name: "allergies",
  type: "text",
  allowMultipleValues: true,
  // ... other properties
};

const ccPatientField = {
  values: [
    { value: "Peanuts" },
    { value: "Shellfish" },
    { value: "Latex" }
  ],
  field: ccField
};

// Converts to AP TEXTBOX_LIST
const result = await convertCCToAP(ccField, ccPatientField, apField);
// Result: { field_value: { "opt1": "Peanuts", "opt2": "Shellfish", "opt3": "Latex" } }
```

### Example 2: CC Boolean → AP RADIO

```typescript
// CC boolean field (smoker status)
const ccField = {
  id: 789,
  name: "smoker",
  type: "boolean",
  allowMultipleValues: false
};

const ccPatientField = {
  values: [{ value: "true" }],
  field: ccField
};

// Converts to AP RADIO with international options
const result = await convertCCToAP(ccField, ccPatientField, apField);
// Result: { value: "Yes" }
// AP field options: ["Yes", "Ja", "No", "Nein"]
```

## Configuration

### Default Configuration

```typescript
const DEFAULT_CONVERSION_CONFIG = {
  multiValueStrategy: "textbox_list",
  booleanOptions: {
    yesValues: ["yes", "ja", "true", "1", "y"],
    noValues: ["no", "nein", "false", "0", "n"],
    defaultYesValue: "Yes",
    defaultNoValue: "No"
  },
  textboxListConfig: {
    generateOptionIds: true,
    preserveOrder: true,
    allowEmptyValues: false,
    maxOptions: 100
  },
  allowFallbackToText: true,
  preserveEmptyValues: false
};
```

### Custom Configuration

```typescript
const service = new FieldValueConversionService({
  booleanOptions: {
    defaultYesValue: "Oui",
    defaultNoValue: "Non"
  },
  textboxListConfig: {
    maxOptions: 50
  }
});
```

## Validation

The system includes comprehensive validation for all field types:

- **Email**: RFC-compliant email validation with normalization
- **Phone**: International phone number format validation
- **Date**: ISO 8601 date format validation and normalization
- **Numerical**: Number validation with type coercion
- **Text**: Basic string validation

## Error Handling

All conversion functions return `ConversionResult<T>` with:

```typescript
interface ConversionResult<T> {
  success: boolean;
  value?: T;
  error?: ConversionError;
  warnings?: string[];
  metadata?: {
    originalValue?: unknown;
    conversionType?: string;
    optionsMapped?: number;
    optionsSkipped?: number;
  };
}
```

Error types include:
- `FIELD_TYPE_MISMATCH`
- `INVALID_VALUE_FORMAT`
- `VALIDATION_FAILED`
- `OPTION_NOT_FOUND`
- `API_ERROR`

## Field Type Compatibility

The system maintains a compatibility matrix for field type conversions:

```typescript
// AP → CC compatibility
"TEXT": ["text", "textarea", "email", "telephone"]
"TEXTBOX_LIST": ["text", "textarea", "select"]
"RADIO": ["select", "boolean"]

// CC → AP compatibility  
"text": ["TEXT", "LARGE_TEXT", "TEXTBOX_LIST"]
"boolean": ["RADIO"]
"select": ["SINGLE_OPTIONS", "MULTIPLE_OPTIONS", "TEXTBOX_LIST"]
```

## Performance Considerations

- **Batch Processing**: Support for batch conversions with configurable batch sizes
- **Caching**: Field mapping and option caching with TTL
- **Validation**: Early validation to prevent API errors
- **Retry Logic**: Exponential backoff for transient failures

## Testing

Run the examples to test the conversion system:

```typescript
import { runAllExamples } from './values/examples';

await runAllExamples();
```

## Integration

The value conversion system integrates with:

- **Field Mapping Service**: Uses field mappings for conversion context
- **API Clients**: Fetches fresh field data for accurate conversions  
- **Database**: Stores conversion results and mappings
- **Logging**: Comprehensive logging for debugging and monitoring

## Specification Compliance

This implementation fully complies with `field-conversion-specification.md`:

✅ **Multi-Value Rule**: All CC multi-value fields → AP TEXTBOX_LIST  
✅ **Boolean Conversion**: CC boolean ↔ AP RADIO with international support  
✅ **Medical Fields**: Proper handling of medication/diagnosis fields  
✅ **Select Mapping**: CC select → AP SINGLE_OPTIONS/MULTIPLE_OPTIONS  
✅ **Standard Fields**: Email/phone standard field conversions  
✅ **Validation**: Comprehensive validation and error handling  
✅ **Data Integrity**: Preserves semantic meaning during conversion  

## Future Enhancements

- **Custom Validation Rules**: User-defined validation for specific field types
- **Conversion Plugins**: Extensible conversion system for custom field types
- **Performance Metrics**: Detailed conversion performance tracking
- **Conflict Resolution**: Advanced conflict resolution for bidirectional sync
