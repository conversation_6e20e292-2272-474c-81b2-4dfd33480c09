/**
 * Field Value Conversion Service
 * 
 * This service orchestrates all field value conversions between AutoPatient (AP)
 * and CliniCore (CC) systems according to the field-conversion-specification.md.
 */

import type {
  ConversionContext,
  ConversionResult,
  ConversionError,
  FieldConversionConfig,
  BatchConversionRequest,
  BatchConversionResult,
  APFieldType,
  CCFieldType,
  CCFieldValue,
  APTextboxListValue,
  APCustomFieldValue
} from "./types";
import { ConversionErrorType } from "./types";
import type { GetCCCustomField, GetCCPatientCustomField, APGetCustomFieldType } from "@/type";
import {
  convertCCMultiValueToAPTextboxList,
  convertAPTextboxListToCCMultiValue,
  convertCCBooleanToAPRadio,
  convertAPRadioToCCBoolean,
  convertSingleValue,
  createBooleanRadioOptions,
  DEFAULT_BOOLEAN_OPTIONS
} from "./converters";
import { validateFieldValue, areFieldTypesCompatible } from "./validation";
import { logInfo, logWarn, logError, logDebug } from "@/utils/logger";

/**
 * Default field conversion configuration
 */
export const DEFAULT_CONVERSION_CONFIG: FieldConversionConfig = {
  multiValueStrategy: "textbox_list",
  booleanOptions: DEFAULT_BOOLEAN_OPTIONS,
  textboxListConfig: {
    generateOptionIds: true,
    preserveOrder: true,
    allowEmptyValues: false,
    maxOptions: 100
  },
  validationRules: [],
  allowFallbackToText: true,
  preserveEmptyValues: false
};

/**
 * Main field value conversion service
 */
export class FieldValueConversionService {
  private config: FieldConversionConfig;

  constructor(config: Partial<FieldConversionConfig> = {}) {
    this.config = { ...DEFAULT_CONVERSION_CONFIG, ...config };
  }

  /**
   * Convert CC field value to AP field value
   */
  async convertCCToAP(
    ccField: GetCCCustomField,
    ccPatientField: GetCCPatientCustomField,
    apField: APGetCustomFieldType
  ): Promise<ConversionResult<APCustomFieldValue>> {
    try {
      logDebug("Converting CC field to AP", {
        ccFieldId: ccField.id,
        ccFieldName: ccField.name,
        ccFieldType: ccField.type,
        apFieldId: apField.id,
        apFieldName: apField.name,
        apFieldType: apField.dataType,
        valuesCount: ccPatientField.values?.length || 0
      });

      // Handle empty values
      if (!ccPatientField.values || ccPatientField.values.length === 0) {
        return {
          success: true,
          value: { id: apField.id, value: "" },
          warnings: ["No values found in CC field"]
        };
      }

      const ccValues = ccPatientField.values.filter(v => v.value && v.value.trim() !== "");

      if (ccValues.length === 0) {
        return {
          success: true,
          value: { id: apField.id, value: "" },
          warnings: ["All CC values were empty"]
        };
      }

      // Determine conversion strategy based on field types and multi-value support
      const isMultiValue = ccField.allowMultipleValues && ccValues.length > 1;
      const apFieldType = apField.dataType as APFieldType;
      const ccFieldType = ccField.type as CCFieldType;

      // Multi-value conversions
      if (isMultiValue) {
        return await this.convertCCMultiValueToAP(ccValues, ccField, apField);
      }

      // Single value conversions
      return this.convertCCSingleValueToAP(ccValues[0], ccField, apField);

    } catch (error) {
      logError("Failed to convert CC field to AP", {
        ccFieldId: ccField.id,
        apFieldId: apField.id,
        error: error instanceof Error ? error.message : String(error)
      });

      return {
        success: false,
        error: {
          type: ConversionErrorType.API_ERROR,
          message: `Failed to convert CC field to AP: ${error instanceof Error ? error.message : String(error)}`,
          fieldId: ccField.id.toString(),
          fieldName: ccField.name,
          recoverable: true
        }
      };
    }
  }

  /**
   * Convert AP field value to CC field value
   */
  async convertAPToCC(
    apField: APGetCustomFieldType,
    apValue: APCustomFieldValue,
    ccField: GetCCCustomField
  ): Promise<ConversionResult<CCFieldValue[]>> {
    try {
      logDebug("Converting AP field to CC", {
        apFieldId: apField.id,
        apFieldName: apField.name,
        apFieldType: apField.dataType,
        ccFieldId: ccField.id,
        ccFieldName: ccField.name,
        ccFieldType: ccField.type,
        hasFieldValue: !!apValue.field_value,
        hasValue: !!apValue.value
      });

      const apFieldType = apField.dataType as APFieldType;
      const ccFieldType = ccField.type as CCFieldType;

      // Handle TEXTBOX_LIST conversion
      if (apFieldType === "TEXTBOX_LIST" && apValue.field_value) {
        return convertAPTextboxListToCCMultiValue(
          { field_value: apValue.field_value },
          apField,
          ccField
        );
      }

      // Handle RADIO to boolean conversion
      if (apFieldType === "RADIO" && ccFieldType === "boolean" && apValue.value) {
        const booleanResult = convertAPRadioToCCBoolean(
          String(apValue.value),
          this.config.booleanOptions
        );
        
        if (booleanResult.success) {
          return {
            success: true,
            value: [{ value: String(booleanResult.value) }],
            metadata: booleanResult.metadata
          };
        } else {
          return {
            success: false,
            error: booleanResult.error
          };
        }
      }

      // Handle single value conversion
      if (apValue.value !== undefined) {
        const singleResult = convertSingleValue(
          apValue.value,
          apFieldType,
          ccFieldType,
          apField.id,
          apField.name
        );

        if (singleResult.success) {
          return {
            success: true,
            value: [{ value: String(singleResult.value) }],
            metadata: singleResult.metadata
          };
        } else {
          return {
            success: false,
            error: singleResult.error
          };
        }
      }

      // No value to convert
      return {
        success: true,
        value: [],
        warnings: ["No value found in AP field"]
      };

    } catch (error) {
      logError("Failed to convert AP field to CC", {
        apFieldId: apField.id,
        ccFieldId: ccField.id,
        error: error instanceof Error ? error.message : String(error)
      });

      return {
        success: false,
        error: {
          type: ConversionErrorType.API_ERROR,
          message: `Failed to convert AP field to CC: ${error instanceof Error ? error.message : String(error)}`,
          fieldId: apField.id,
          fieldName: apField.name,
          recoverable: true
        }
      };
    }
  }

  /**
   * Convert CC multi-value to AP field
   */
  private async convertCCMultiValueToAP(
    ccValues: CCFieldValue[],
    ccField: GetCCCustomField,
    apField: APGetCustomFieldType
  ): Promise<ConversionResult<APCustomFieldValue>> {
    const apFieldType = apField.dataType as APFieldType;

    // All CC multi-value fields convert to AP TEXTBOX_LIST (per specification)
    if (apFieldType === "TEXTBOX_LIST") {
      const textboxResult = await convertCCMultiValueToAPTextboxList(ccValues, apField);
      
      if (textboxResult.success) {
        return {
          success: true,
          value: {
            id: apField.id,
            field_value: textboxResult.value?.field_value
          },
          warnings: textboxResult.warnings,
          metadata: textboxResult.metadata
        };
      } else {
        return {
          success: false,
          error: textboxResult.error
        };
      }
    }

    // Handle MULTIPLE_OPTIONS for CC select fields
    if (apFieldType === "MULTIPLE_OPTIONS" && ccField.type === "select") {
      // Convert to array of values, filtering out undefined
      const values = ccValues.map(v => v.value).filter((v): v is string => v !== undefined);
      return {
        success: true,
        value: {
          id: apField.id,
          value: values
        },
        metadata: {
          originalValue: ccValues,
          conversionType: "cc_select_multi_to_ap_multiple_options"
        }
      };
    }

    // Fallback: convert to pipe-separated text (not recommended per spec)
    logWarn("Falling back to pipe-separated text for CC multi-value", {
      ccFieldId: ccField.id,
      ccFieldType: ccField.type,
      apFieldType
    });

    const pipeValue = ccValues.map(v => v.value).filter(v => v !== undefined).join(" | ");
    return {
      success: true,
      value: {
        id: apField.id,
        value: pipeValue
      },
      warnings: ["Used pipe-separated fallback for multi-value conversion"],
      metadata: {
        originalValue: ccValues,
        conversionType: "cc_multi_to_ap_pipe_separated"
      }
    };
  }

  /**
   * Convert CC single value to AP field
   */
  private convertCCSingleValueToAP(
    ccValue: CCFieldValue,
    ccField: GetCCCustomField,
    apField: APGetCustomFieldType
  ): ConversionResult<APCustomFieldValue> {
    const apFieldType = apField.dataType as APFieldType;
    const ccFieldType = ccField.type as CCFieldType;

    // Handle boolean to RADIO conversion
    if (ccFieldType === "boolean" && apFieldType === "RADIO") {
      const value = ccValue.value || "false";
      const booleanValue = value.toLowerCase() === "true";
      const radioResult = convertCCBooleanToAPRadio(booleanValue, this.config.booleanOptions);
      
      if (radioResult.success) {
        return {
          success: true,
          value: {
            id: apField.id,
            value: radioResult.value
          },
          metadata: radioResult.metadata
        };
      } else {
        return {
          success: false,
          error: radioResult.error
        };
      }
    }

    // Handle single value conversion
    const singleResult = convertSingleValue(
      ccValue.value || "",
      ccFieldType,
      apFieldType,
      ccField.id.toString(),
      ccField.name
    );

    if (singleResult.success) {
      return {
        success: true,
        value: {
          id: apField.id,
          value: singleResult.value as string | number | string[]
        },
        metadata: singleResult.metadata
      };
    } else {
      return {
        success: false,
        error: singleResult.error
      };
    }
  }

  /**
   * Process batch conversions
   */
  async processBatchConversions(
    request: BatchConversionRequest
  ): Promise<BatchConversionResult> {
    const results: ConversionResult[] = [];
    const errors: ConversionError[] = [];
    let successful = 0;
    let failed = 0;
    let warnings = 0;

    for (const conversion of request.conversions) {
      try {
        // This would need to be implemented based on the specific conversion context
        // For now, return a placeholder result
        const result: ConversionResult = {
          success: true,
          value: conversion.sourceValue
        };

        results.push(result);
        
        if (result.success) {
          successful++;
          if (result.warnings && result.warnings.length > 0) {
            warnings++;
          }
        } else {
          failed++;
          if (result.error) {
            errors.push(result.error);
          }
        }
      } catch (error) {
        failed++;
        const conversionError: ConversionError = {
          type: ConversionErrorType.API_ERROR,
          message: `Batch conversion failed: ${error instanceof Error ? error.message : String(error)}`,
          fieldId: "",
          fieldName: "",
          recoverable: true
        };
        errors.push(conversionError);
        results.push({
          success: false,
          error: conversionError
        });
      }
    }

    return {
      results,
      summary: {
        total: request.conversions.length,
        successful,
        failed,
        warnings
      },
      errors
    };
  }
}
